/* Match Header Enhancements */
.match-header-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #404040;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.match-header-card .card-body {
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
}

.vs-circle {
    background: linear-gradient(45deg, #007bff, #0056b3);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    transition: transform 0.3s ease;
}

.vs-circle:hover {
    transform: scale(1.1);
}

.team-name {
    transition: color 0.3s ease, text-shadow 0.3s ease;
    margin-top: 15px;
}

.team-name:hover {
    color: #007bff !important;
    text-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
}

.match-detail-box {
    background: rgba(108, 117, 125, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    height: 70px;
}

.match-detail-box:hover {
    background: rgba(108, 117, 125, 0.25);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.countdown-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Action buttons enhancement */
.btn-outline-light:hover, .btn-outline-info:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    color: black !important;
}

/* Enhanced "Marcar como Revisado" button styling */
button[name="sub_marcar_revisado_probabilidades"] {
    font-size: 14px !important;
    font-weight: 600 !important;
    padding: 12px 20px !important;
    border: 2px solid #00acac !important;
    background: transparent !important;
    color: #00acac !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

button[name="sub_marcar_revisado_probabilidades"]:hover {
    background: #00acac !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 172, 172, 0.3) !important;
    border-color: #00acac !important;
}

button[name="sub_marcar_revisado_probabilidades"]:active {
    transform: translateY(0px) !important;
    box-shadow: 0 4px 15px rgba(0, 172, 172, 0.2) !important;
}

/* Pulse animation for "Marcar como Revisado" buttons */
button[name="sub_marcar_revisado_probabilidades"]::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(0, 172, 172, 0.3);
    transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
    transform: translate(-50%, -50%);
    z-index: -1;
}

button[name="sub_marcar_revisado_probabilidades"]:hover::before {
    width: 300px;
    height: 300px;
}

/* Equal width tabs for Goles, Corners Superior, and Corners Inferior sections */
.region_NAVTAB_HEAD.nav-tabs {
    display: flex;
}

.region_NAVTAB_HEAD.nav-tabs .nav-item {
    flex: 1;
}

.region_NAVTAB_HEAD.nav-tabs .nav-item .nav-link {
    text-align: center;
    width: 100%;
}

/* Historical table separators */
.historical-separator-5 {
    border-bottom: 2px solid #007bff !important;
}

.historical-separator-10 {
    border-bottom: 2px solid #28a745 !important;
}

/* Color-coded criteria headers */
.home-criteria-header {
    color: #007bff !important; /* Blue color for home team criteria */
    font-weight: 600 !important;
}

.away-criteria-header {
    color: #ffc107 !important; /* Yellow color for away team criteria */
    font-weight: 600 !important;
}

/* Floating team badges */
.floating-team-badges {
    position: fixed;
    bottom: 19px;
    right: 55px; /* Position to the left of scroll-to-top button */
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.floating-badge {
    min-width: 120px;
    height: 40px;
    border-radius: 20px;
    display: none; /* Initially hidden, will be shown/hidden with scroll behavior */
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: none;
    padding: 12px 16px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.floating-badge:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.home-badge {
    background-color: #007bff !important; /* Blue color for home team */
    color: white !important;
}

.home-badge:hover {
    background-color: #0056b3 !important;
    color: white !important;
}

.away-badge {
    background-color: #ffc107 !important; /* Yellow color for away team */
    color: #212529 !important;
}

.away-badge:hover {
    background-color: #e0a800 !important;
    color: #212529 !important;
}

/* Toast notification for floating badges */
.floating-badge-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    z-index: 9999;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.floating-badge-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.floating-badge-toast.hide {
    opacity: 0;
    transform: translateX(100%);
}
