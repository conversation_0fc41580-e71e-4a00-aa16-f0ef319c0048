<?php
#region region DOCS
/** @var array $partidosBets */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Apuestas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <style>
        .criteria-row {
            background-color: var(--bs-gray-100) !important;
        }
        .criteria-row td {
            border-top: none !important;
            padding: 0 !important;
        }
        .toggle-criteria {
            transition: all 0.3s ease;
        }
        .toggle-criteria:hover {
            transform: scale(1.1);
        }
        .card-sm {
            border: 1px solid var(--bs-border-color);
            transition: all 0.2s ease;
        }
        .card-sm:hover {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        .bet-row:hover {
            background-color: var(--bs-gray-50) !important;
        }
        .criteria-expand-animation {
            animation: slideDown 0.3s ease-out;
        }
        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 500px;
            }
        }

        /* Fixture Column Styling */
        .fixture-container {
            min-height: 50px;
            padding: 4px;
            height: 100%;
        }

        .team-badge-container {
            flex: 1;
        }

        .team-name-badge {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .team-name-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .fixture-container {
                min-height: 45px;
            }
        }

        /* Sortable header styles */
        .sortable-header {
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.2s ease;
        }
        .sortable-header::after {
            content: '\f0dc';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.5;
            font-size: 12px;
        }
        .sortable-header.sort-asc::after {
            content: '\f0de';
            opacity: 1;
            color: var(--bs-primary);
        }
        .sortable-header.sort-desc::after {
            content: '\f0dd';
            opacity: 1;
            color: var(--bs-primary);
        }

        /* Table enhancements */
        #betsTable {
            border-collapse: separate;
            border-spacing: 0;
        }
        #betsTable thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 2px solid var(--bs-primary);
        }
        #betsTable tbody tr:hover {
            background-color: var(--bs-gray-100) !important;
        }

        /* Checkbox styling */
        .bet-checkbox {
            transform: scale(1.2);
        }
        .bet-checkbox:checked {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
        }

        /* Form styling */
        #createBetForm .form-control:focus {
            border-color: var(--bs-primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
        }

        /* Color-coded criteria headers */
        .home-criteria-header {
            color: #007bff !important; /* Blue color for home team criteria */
            font-weight: 600 !important;
        }

        .away-criteria-header {
            color: #ffc107 !important; /* Yellow color for away team criteria */
            font-weight: 600 !important;
        }

        .criteria-section-header {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border: 1px solid #404040;
            color: #ffffff;
            font-weight: 600;
            text-align: center;
            padding: 8px;
            margin-bottom: 15px;
            border-radius: 4px;
        }

        .criteria-section-header.analysis-10 {
            background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%);
        }
    </style>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h1 class="page-header mb-0">Listado de Apuestas</h1>
            <div>
                <a href="lpartidos" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-list me-1"></i>Ver Partidos
                </a>
                <a href="partidos-bets-realizados" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-history me-1"></i>Ver apuestas realizadas
                </a>
            </div>
        </div>

        <hr>
        <!-- END page-header -->



        <!-- BEGIN panel -->
        <div class="panel panel-inverse">
            <!-- BEGIN panel-heading -->
            <div class="panel-heading">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="panel-title mb-0">Apuestas Registradas</h4>
                    <div class="d-flex align-items-center">
                        <button type="button" class="btn btn-sm btn-outline-light ms-2" id="toggleAllCriteria">
                            <i class="fas fa-expand-alt me-1"></i>Mostrar Todos los Criterios
                        </button>
                    </div>
                </div>
            </div>
            <!-- END panel-heading -->

            <!-- BEGIN panel-info -->
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Tip:</strong> Haz clic en el botón <i class="fas fa-chevron-down mx-1"></i> de cada fila para ver los criterios específicos de esa apuesta.
                Cada apuesta tiene sus propios criterios únicos con nombres y valores independientes.
                También puedes usar el botón "Mostrar Todos los Criterios" o presionar <kbd>Ctrl+E</kbd> para expandir todas las filas.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <!-- END panel-info -->

            <!-- BEGIN bet creation form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Crear Nueva Apuesta
                    </h5>
                </div>
                <div class="card-body">
                    <form id="createBetForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="valorApostado" class="form-label">Valor apostado</label>
                                <input type="text" class="form-control" id="valorApostado" name="valorApostado"
                                       data-type="currencysinsigno" placeholder="5.000" required>
                                <div class="form-text">Formato: 5.000 (pesos colombianos)</div>
                            </div>
                            <div class="col-md-6">
                                <label for="cuota" class="form-label">Cuota</label>
                                <input type="number" class="form-control" id="cuota" name="cuota"
                                       step="0.01" min="1.01" placeholder="1.20" required>
                                <div class="form-text">Formato decimal: 1.20</div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6 mb-2 mb-md-0">
                                <button type="submit" class="btn btn-primary w-100" id="guardarApuesta">
                                    <i class="fas fa-save me-2"></i>Guardar apuesta
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-danger w-100" id="bulkDeleteBtn">
                                    <i class="fas fa-trash me-2"></i>Eliminar Seleccionados
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!-- END bet creation form -->

            <!-- BEGIN panel-body -->
            <div class="panel-body">
                <?php if (empty($partidosBets)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No hay apuestas registradas en este momento.
                    </div>
                <?php else: ?>
                    <!-- BEGIN search and filter controls -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Buscar por partido, tipo de apuesta...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterByType">
                                <option value="">Todos los tipos</option>
                                <?php
                                $uniqueTypes = [];
                                foreach ($partidosBets as $bet) {
                                    if (!empty($bet['tipo_apuesta'])) {
                                        $uniqueTypes[$bet['tipo_apuesta']] = true;
                                    }
                                }
                                foreach (array_keys($uniqueTypes) as $type):
                                ?>
                                    <option value="<?php echo htmlspecialchars($type); ?>"><?php echo htmlspecialchars($type); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                                <i class="fas fa-times me-1"></i>Limpiar Filtros
                            </button>
                        </div>
                    </div>
                    <!-- END search and filter controls -->

                    <?php
                    // Function to determine if a criterion is home or away team related
                    function getCriterionClass($criterionName) {
                        if (strpos($criterionName, '(H)') !== false) {
                            return 'home-criteria-header';
                        } elseif (strpos($criterionName, '(A)') !== false) {
                            return 'away-criteria-header';
                        }
                        return ''; // Default for neutral criteria
                    }

                    // Function to format criterion values as requested:
                    // - 50 to 100 (inclusive): show as integer percentage with no decimals
                    // - Otherwise: show with 2 decimal places
                    function formatCriterionValue($value) {
                        if ($value === null || $value === '') {
                            return 'N/A';
                        }
                        $num = (float)$value;
                        if ($num >= 50 && $num <= 100) {
                            return number_format($num, 0) . '%';
                        }
                        return number_format($num, 2);
                    }
                    ?>

                    <div class="table-responsive">
                        <table class="table table-striped table-bordered" id="betsTable">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width: 40px;">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th class="text-center" style="width: 120px;">Acciones</th>
                                    <th class="text-center sortable-header" data-column="2">Partido</th>
                                    <th class="text-center sortable-header" data-column="3">Fecha del partido</th>
                                    <th class="text-center sortable-header" data-column="4">Tipo de apuesta</th>
                                    <th class="text-center sortable-header" data-column="5">Cuota</th>
                                    <th class="text-center sortable-header" data-column="6">Probabilidad</th>
                                    <th class="text-center">Criterios</th>
                                </tr>
                            </thead>
                            <tbody class="fs-13px">
                                <?php foreach ($partidosBets as $index => $bet): ?>
                                    <tr class="bet-row" data-bet-index="<?php echo $index; ?>">
                                        <td class="text-center align-middle">
                                            <input type="checkbox" class="form-check-input bet-checkbox"
                                                   value="<?php echo htmlspecialchars($bet['pbd_id']); ?>"
                                                   data-bet-index="<?php echo $index; ?>">
                                        </td>
                                        <td class="text-center align-middle">
                                            <div class="d-flex justify-content-center align-items-center gap-1">
                                                <button class="btn btn-sm btn-outline-primary toggle-criteria" type="button" data-bet-index="<?php echo $index; ?>">
                                                    <i class="fas fa-chevron-down"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDeactivateBet('<?php echo htmlspecialchars($bet['pbd_id']); ?>')"
                                                        title="Desactivar apuesta">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if (!empty($bet['home']) && !empty($bet['away'])): ?>
                                                <!-- Enhanced Fixture Display -->
                                                <div class="fixture-container d-flex flex-column align-items-center justify-content-center">
                                                    <!-- Teams Row -->
                                                    <div class="d-flex align-items-center justify-content-center w-100 mb-1">
                                                        <!-- Home Team -->
                                                        <div class="team-badge-container me-2">
                                                            <span class="badge bg-primary rounded-pill fs-14px px-2 py-1 cursor-pointer team-name-badge"
                                                                  id="home_bet_<?php echo $index; ?>"
                                                                  onclick="copyAndShowTooltip('home_bet_<?php echo $index; ?>')"
                                                                  title="<?php echo htmlspecialchars($bet['home']); ?> - Click to copy">
                                                                <i class="fas fa-home me-1"></i>
                                                                <span class="team-text"><?php echo htmlspecialchars($bet['home']); ?></span>
                                                            </span>
                                                        </div>

                                                        <!-- VS Separator -->
                                                        <div class="mx-1">
                                                            <span class="badge bg-dark rounded-pill fs-12px px-2 py-1">
                                                                VS
                                                            </span>
                                                        </div>

                                                        <!-- Away Team -->
                                                        <div class="team-badge-container ms-2">
                                                            <span class="badge bg-warning rounded-pill fs-14px px-2 py-1 cursor-pointer team-name-badge"
                                                                  id="away_bet_<?php echo $index; ?>"
                                                                  onclick="copyAndShowTooltip('away_bet_<?php echo $index; ?>')"
                                                                  title="<?php echo htmlspecialchars($bet['away']); ?> - Click to copy">
                                                                <span class="team-text"><?php echo htmlspecialchars($bet['away']); ?></span>
                                                                <i class="fas fa-plane ms-1"></i>
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <!-- Tournament Badge -->
                                                    <?php if (!empty($bet['pais_nombre'])): ?>
                                                        <div class="tournament-info pt-3">
                                                            <span class="badge bg-secondary rounded-0 fs-13px px-2 py-1">
                                                                <i class="fas fa-trophy me-1 text-success"></i>
                                                                <?php echo htmlspecialchars($bet['pais_nombre']); ?>
                                                            </span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if (!empty($bet['fecha'])): ?>
                                                <?php
                                                // Format date and time with AM/PM format
                                                $fechaHoraFormatted = '';
                                                $countdownData = '';

                                                if (!empty($bet['fecha']) && !empty($bet['horamilitar'])) {
                                                    // Convert horamilitar to 24-hour format
                                                    $hora24 = format_date_to_hora_24_format_HHMM($bet['horamilitar']);
                                                    $hora12 = $hora24; // fallback

                                                    try {
                                                        $dt = DateTime::createFromFormat('H:i', $hora24, new DateTimeZone('America/Bogota'));
                                                        if ($dt instanceof DateTime) {
                                                            $hora12 = $dt->format('g:i A'); // e.g., 2:30 PM
                                                        }
                                                    } catch (Exception $e) {
                                                        // keep fallback
                                                    }

                                                    $fechaHoraFormatted = $bet['fecha'] . ' ' . $hora12;
                                                    $countdownData = $bet['fecha'] . ' ' . $bet['horamilitar'];
                                                } else {
                                                    $fechaHoraFormatted = $bet['fecha'];
                                                }
                                                ?>
                                                <div class="fw-bold"><?php echo htmlspecialchars($fechaHoraFormatted); ?></div>
                                                <?php if (!empty($countdownData)): ?>
                                                    <div class="mt-1 countdown-container" data-match-datetime="<?php echo htmlspecialchars($countdownData); ?>" style="display: none;">
                                                        <div class="badge bg-info countdown-badge fs-10px">
                                                            <span class="countdown-text"></span>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if (!empty($bet['tipo_apuesta'])): ?>
                                                <span class="badge bg-info fs-15px"><?php echo htmlspecialchars($bet['tipo_apuesta']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if (!empty($bet['cuota'])): ?>
                                                <?php
                                                $cuota = (float)$bet['cuota'];
                                                $badgeClass = 'bg-primary';
                                                if ($cuota >= 3.0) $badgeClass = 'bg-danger';
                                                elseif ($cuota >= 2.0) $badgeClass = 'bg-warning';
                                                elseif ($cuota >= 1.5) $badgeClass = 'bg-success';
                                                ?>
                                                <span class="badge <?php echo $badgeClass; ?> fs-12px"><?php echo number_format($cuota, 2); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if (!empty($bet['probabilidad'])): ?>
                                                <?php
                                                $probabilidad = (int)$bet['probabilidad'];
                                                ?>
                                                <span class="badge bg-info fs-15px"><?php echo $probabilidad; ?>%</span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php
                                            // Count how many criteria this bet has
                                            $criteriaCount = 0;
                                            for ($i = 1; $i <= 12; $i++) {
                                                if (!empty($bet["criterio_nombre_$i"])) {
                                                    $criteriaCount++;
                                                }
                                            }
                                            ?>
                                            <?php if ($criteriaCount > 0): ?>
                                                <span class="badge bg-secondary"><?php echo $criteriaCount; ?> criterios</span>
                                            <?php else: ?>
                                                <span class="text-muted">Sin criterios</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <!-- Expandable criteria row -->
                                    <tr class="criteria-row" id="criteria-<?php echo $index; ?>" style="display: none;">
                                        <td colspan="8" class="bg-light">
                                            <div class="p-3">
                                                <h6 class="mb-3"><i class="fas fa-list-ul me-2"></i>Criterios de la Apuesta</h6>
                                                <?php if ($criteriaCount > 0): ?>
                                                    <?php
                                                    // Group criteria by unique criterion types
                                                    // Each criterion type appears twice: first occurrence for 5-match, second for 10-match
                                                    $criteriaGroups = [
                                                        '5_partidos' => [],
                                                        '10_partidos' => []
                                                    ];

                                                    $seenCriteria = []; // Track which criterion types we've seen

                                                    // Process all criteria and group by first/second occurrence
                                                    for ($i = 1; $i <= 12; $i++) {
                                                        if (!empty($bet["criterio_nombre_$i"])) {
                                                            $criterionName = $bet["criterio_nombre_$i"];
                                                            $criterionValue = $bet["criterio_valor_$i"];

                                                            // Check if we've seen this criterion type before
                                                            if (!isset($seenCriteria[$criterionName])) {
                                                                // First occurrence - goes to 5 partidos group
                                                                $criteriaGroups['5_partidos'][] = [
                                                                    'name' => $criterionName,
                                                                    'value' => $criterionValue,
                                                                    'index' => $i
                                                                ];
                                                                $seenCriteria[$criterionName] = true;
                                                            } else {
                                                                // Second occurrence - goes to 10 partidos group
                                                                $criteriaGroups['10_partidos'][] = [
                                                                    'name' => $criterionName,
                                                                    'value' => $criterionValue,
                                                                    'index' => $i
                                                                ];
                                                            }
                                                        }
                                                    }

                                                    $has5MatchCriteria = !empty($criteriaGroups['5_partidos']);
                                                    $has10MatchCriteria = !empty($criteriaGroups['10_partidos']);
                                                    ?>

                                                    <?php if ($has5MatchCriteria): ?>
                                                        <!-- 5 Partidos Section -->
                                                        <div class="criteria-section-header">
                                                            5 Partidos
                                                        </div>
                                                        <div class="row mb-4">
                                                            <?php foreach ($criteriaGroups['5_partidos'] as $criterion): ?>
                                                                <div class="col-md-6 col-lg-3 mb-2">
                                                                    <div class="card card-sm">
                                                                        <div class="card-body p-2">
                                                                            <div class="d-flex justify-content-between align-items-center">
                                                                                <small class="fw-bold fs-13px <?php echo getCriterionClass($criterion['name']); ?>">
                                                                                    <?php echo htmlspecialchars($criterion['name']); ?>
                                                                                </small>
                                                                                <span class="badge bg-primary fs-13px">
                                                                                    <?php echo formatCriterionValue($criterion['value']); ?>
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php endif; ?>

                                                    <?php if ($has10MatchCriteria): ?>
                                                        <!-- 10 Partidos Section -->
                                                        <div class="criteria-section-header analysis-10">
                                                            10 Partidos
                                                        </div>
                                                        <div class="row">
                                                            <?php foreach ($criteriaGroups['10_partidos'] as $criterion): ?>
                                                                <div class="col-md-6 col-lg-3 mb-2">
                                                                    <div class="card card-sm">
                                                                        <div class="card-body p-2">
                                                                            <div class="d-flex justify-content-between align-items-center">
                                                                                <small class="fw-bold fs-13px <?php echo getCriterionClass($criterion['name']); ?>">
                                                                                    <?php echo htmlspecialchars($criterion['name']); ?>
                                                                                </small>
                                                                                <span class="badge bg-primary fs-13px">
                                                                                    <?php echo formatCriterionValue($criterion['value']); ?>
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        Esta apuesta no tiene criterios definidos.
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
            <!-- END panel-body -->
        </div>
        <!-- END panel -->
    </div>
    <!-- END #content -->
</div>
<!-- END #app -->

<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<!-- Currency formatting script -->
<script src="resources/js/formatcurrency2.js"></script>

<!-- BEGIN JavaScript functionality -->
<script>
$(document).ready(function() {
    // Handle expand/collapse functionality
    $('.toggle-criteria').on('click', function() {
        const betIndex = $(this).data('bet-index');
        const criteriaRow = $('#criteria-' + betIndex);
        const icon = $(this).find('i');
        const button = $(this);

        if (criteriaRow.is(':visible')) {
            // Collapse with animation
            criteriaRow.slideUp(300, function() {
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                button.removeClass('btn-primary').addClass('btn-outline-primary');
            });
        } else {
            // Expand with animation
            criteriaRow.slideDown(300);
            icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            button.removeClass('btn-outline-primary').addClass('btn-primary');

            // Add animation class to the content
            criteriaRow.find('.p-3').addClass('criteria-expand-animation');
            setTimeout(() => {
                criteriaRow.find('.p-3').removeClass('criteria-expand-animation');
            }, 300);
        }
    });

    // Toggle all criteria functionality
    let allExpanded = false;
    $('#toggleAllCriteria').on('click', function() {
        const button = $(this);
        const icon = button.find('i');

        if (!allExpanded) {
            // Expand all
            $('.toggle-criteria').each(function() {
                const betIndex = $(this).data('bet-index');
                const criteriaRow = $('#criteria-' + betIndex);
                const rowIcon = $(this).find('i');

                if (!criteriaRow.is(':visible')) {
                    criteriaRow.slideDown(200);
                    rowIcon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                    $(this).removeClass('btn-outline-primary').addClass('btn-primary');
                }
            });

            button.html('<i class="fas fa-compress-alt me-1"></i>Ocultar Todos los Criterios');
            allExpanded = true;
        } else {
            // Collapse all
            $('.toggle-criteria').each(function() {
                const betIndex = $(this).data('bet-index');
                const criteriaRow = $('#criteria-' + betIndex);
                const rowIcon = $(this).find('i');

                if (criteriaRow.is(':visible')) {
                    criteriaRow.slideUp(200);
                    rowIcon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                    $(this).removeClass('btn-primary').addClass('btn-outline-primary');
                }
            });

            button.html('<i class="fas fa-expand-alt me-1"></i>Mostrar Todos los Criterios');
            allExpanded = false;
        }
    });

    // Keyboard shortcut: Ctrl+E to expand all
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            $('#toggleAllCriteria').click();
        }
    });

    // Search and filter functionality
    function filterTable() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const typeFilter = $('#filterByType').val();

        $('#betsTable tbody tr.bet-row').each(function() {
            const row = $(this);
            const betIndex = row.data('bet-index');
            const criteriaRow = $('#criteria-' + betIndex);

            // Get row data
            const partido = row.find('td').eq(2).text().toLowerCase();
            const tipoApuesta = row.find('td').eq(4).text().toLowerCase();

            // Check search term
            const matchesSearch = searchTerm === '' ||
                                partido.includes(searchTerm) ||
                                tipoApuesta.includes(searchTerm);

            // Check type filter
            const matchesType = typeFilter === '' || tipoApuesta.includes(typeFilter.toLowerCase());

            // Show/hide row and its criteria
            if (matchesSearch && matchesType) {
                row.show();
                // Keep criteria row hidden unless it was previously expanded
                if (!criteriaRow.is(':visible')) {
                    criteriaRow.hide();
                }
            } else {
                row.hide();
                criteriaRow.hide();
            }
        });

        // Update visible row count
        updateRowCount();
    }

    function updateRowCount() {
        const visibleRows = $('#betsTable tbody tr.bet-row:visible').length;
        const totalRows = $('#betsTable tbody tr.bet-row').length;

        // Update or create row count display
        let countDisplay = $('#rowCount');
        if (countDisplay.length === 0) {
            countDisplay = $('<small id="rowCount" class="text-muted ms-2"></small>');
            $('.table-responsive').before(countDisplay);
        }

        // Show/hide no results message
        let noResultsMsg = $('#noResultsMessage');
        if (visibleRows === 0) {
            if (noResultsMsg.length === 0) {
                noResultsMsg = $(`
                    <div id="noResultsMessage" class="alert alert-warning text-center">
                        <i class="fas fa-search me-2"></i>
                        No se encontraron apuestas que coincidan con los filtros aplicados.
                        <button type="button" class="btn btn-sm btn-outline-warning ms-2" onclick="$('#clearFilters').click()">
                            Limpiar filtros
                        </button>
                    </div>
                `);
                $('.table-responsive').before(noResultsMsg);
            }
            noResultsMsg.show();
            countDisplay.text(`0 de ${totalRows} apuestas`);
        } else {
            noResultsMsg.hide();
            if (visibleRows === totalRows) {
                countDisplay.text(`Mostrando ${totalRows} apuestas`);
            } else {
                countDisplay.text(`Mostrando ${visibleRows} de ${totalRows} apuestas`);
            }
        }
    }

    // Bind search and filter events
    $('#searchInput').on('input', function() {
        filterTable();
        updateSelectAllState();
        updateBulkDeleteButtonVisibility();
    });
    $('#filterByType').on('change', function() {
        filterTable();
        updateSelectAllState();
        updateBulkDeleteButtonVisibility();
    });

    // Clear filters
    $('#clearFilters').on('click', function() {
        $('#searchInput').val('');
        $('#filterByType').val('');
        filterTable();
        updateSelectAllState();
        updateBulkDeleteButtonVisibility();
    });

    // Function to update select all checkbox state
    function updateSelectAllState() {
        const totalVisibleCheckboxes = $('.bet-checkbox:visible').length;
        const checkedVisibleCheckboxes = $('.bet-checkbox:visible:checked').length;

        $('#selectAll').prop('checked', totalVisibleCheckboxes > 0 && totalVisibleCheckboxes === checkedVisibleCheckboxes);
        $('#selectAll').prop('indeterminate', checkedVisibleCheckboxes > 0 && checkedVisibleCheckboxes < totalVisibleCheckboxes);
    }

    // Sort functionality for table headers
    $('.sortable-header').on('click', function() {
        const table = $('#betsTable');
        const tbody = table.find('tbody');
        const rows = tbody.find('tr.bet-row:visible').get();
        const column = $(this).data('column');
        const currentSort = $(this).data('sort') || 'asc';
        const newSort = currentSort === 'asc' ? 'desc' : 'asc';

        // Remove sort indicators from all headers
        $('.sortable-header').removeClass('sort-asc sort-desc');

        // Add sort indicator to current header
        $(this).addClass('sort-' + newSort).data('sort', newSort);

        // Sort visible rows based on column
        rows.sort(function(a, b) {
            let aVal = $(a).find('td').eq(column).text().trim();
            let bVal = $(b).find('td').eq(column).text().trim();

            // Handle date values
            if (column === 3) { // Date column
                aVal = new Date(aVal.split('/').reverse().join('-'));
                bVal = new Date(bVal.split('/').reverse().join('-'));
            }
            // Handle numeric values (cuota)
            else if (column === 5) {
                aVal = parseFloat(aVal) || 0;
                bVal = parseFloat(bVal) || 0;
            }

            if (newSort === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });

        // Rebuild tbody with sorted rows and their corresponding criteria rows
        const allRows = tbody.find('tr').detach();
        rows.forEach(function(row) {
            const betIndex = $(row).data('bet-index');
            const criteriaRow = allRows.filter('#criteria-' + betIndex);
            tbody.append(row);
            if (criteriaRow.length) {
                tbody.append(criteriaRow);
            }
        });

        // Re-append hidden rows at the end
        allRows.filter('tr.bet-row:hidden').each(function() {
            const betIndex = $(this).data('bet-index');
            const criteriaRow = allRows.filter('#criteria-' + betIndex);
            tbody.append(this);
            if (criteriaRow.length) {
                tbody.append(criteriaRow);
            }
        });
    });

    // Initialize row count
    updateRowCount();

    // Initialize bulk delete button state on load
    updateBulkDeleteButtonVisibility();

    // Select all checkbox functionality
    $('#selectAll').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.bet-checkbox:visible').prop('checked', isChecked);
        updateBulkDeleteButtonVisibility();
    });

    // Individual checkbox change handler
    $(document).on('change', '.bet-checkbox', function() {
        updateSelectAllState();
        updateBulkDeleteButtonVisibility();
    });

    // Function to enable/disable bulk delete button based on selected checkboxes (button is always visible)
    function updateBulkDeleteButtonVisibility() {
        const checkedBoxes = $('.bet-checkbox:checked').length;
        if (checkedBoxes > 0) {
            $('#bulkDeleteBtn').prop('disabled', false).removeClass('btn-outline-secondary').addClass('btn-outline-danger');
        } else {
            $('#bulkDeleteBtn').prop('disabled', true).removeClass('btn-outline-danger').addClass('btn-outline-secondary');
        }
    }

    // Bulk delete functionality
    $('#bulkDeleteBtn').on('click', function() {
        const selectedBetDetails = [];
        $('.bet-checkbox:checked').each(function() {
            selectedBetDetails.push($(this).val());
        });

        if (selectedBetDetails.length === 0) {
            swal({
                text: 'No hay apuestas seleccionadas para eliminar.',
                icon: 'warning',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-warning',
                        closeModal: true
                    }
                }
            });
            return;
        }

        swal({
            title: '¿Confirmar eliminación?',
            text: `¿Está seguro de que desea eliminar ${selectedBetDetails.length} apuesta(s) seleccionada(s)? Esta acción no se puede deshacer.`,
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Cancelar',
                    value: null,
                    visible: true,
                    className: 'btn btn-secondary',
                    closeModal: true
                },
                confirm: {
                    text: 'Sí, eliminar',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
                // Disable button to prevent double-click
                const originalText = $('#bulkDeleteBtn').html();
                $('#bulkDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Eliminando...');

                // Send AJAX request
                $.ajax({
                    url: 'partidos-bets',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        action: 'bulk_delete_bet_details',
                        selected_bet_details: selectedBetDetails,
                        ajax_check: 1
                    },
                    success: function(response) {
                        if (response.status === 'success' || response.status === 'partial_success') {
                            swal({
                                title: response.status === 'success' ? '¡Eliminación exitosa!' : '¡Eliminación parcial!',
                                text: response.message,
                                icon: response.status === 'success' ? 'success' : 'warning',
                                buttons: {
                                    confirm: {
                                        text: 'Ok',
                                        value: true,
                                        visible: true,
                                        className: response.status === 'success' ? 'btn btn-success' : 'btn btn-warning',
                                        closeModal: true
                                    }
                                }
                            }).then(() => {
                                // Reload the page to show updated data
                                window.location.reload();
                            });
                        } else {
                            swal({
                                text: 'Error: ' + response.message,
                                icon: 'error',
                                buttons: {
                                    confirm: {
                                        text: 'Ok',
                                        value: true,
                                        visible: true,
                                        className: 'btn btn-danger',
                                        closeModal: true
                                    }
                                }
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);
                        console.error('Response:', xhr.responseText);
                        swal({
                            text: 'Error al procesar la solicitud. Por favor, intente nuevamente.',
                            icon: 'error',
                            buttons: {
                                confirm: {
                                    text: 'Ok',
                                    value: true,
                                    visible: true,
                                    className: 'btn btn-danger',
                                    closeModal: true
                                }
                            }
                        });
                    },
                    complete: function() {
                        // Re-enable button
                        $('#bulkDeleteBtn').prop('disabled', false).html(originalText);
                    }
                });
            }
        });
    });

    // Form submission handler
    $('#createBetForm').on('submit', function(e) {
        e.preventDefault();

        // Get form values
        const valorApostado = $('#valorApostado').val().trim();
        const cuota = $('#cuota').val().trim();

        // Get selected bet details
        const selectedBetDetails = [];
        $('.bet-checkbox:checked').each(function() {
            selectedBetDetails.push($(this).val());
        });

        // Validation
        if (!valorApostado) {
            swal({
                text: 'Por favor, ingrese el valor apostado.',
                icon: 'error',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            }).then(() => {
                $('#valorApostado').focus();
            });
            return;
        }

        if (!cuota || parseFloat(cuota) <= 1) {
            swal({
                text: 'Por favor, ingrese una cuota válida (mayor a 1.00).',
                icon: 'error',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            }).then(() => {
                $('#cuota').focus();
            });
            return;
        }

        if (selectedBetDetails.length === 0) {
            swal({
                text: 'Por favor, seleccione al menos una apuesta de la tabla.',
                icon: 'error',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            });
            return;
        }

        // Disable submit button
        const submitBtn = $('#guardarApuesta');
        const originalText = submitBtn.html();
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Guardando...');

        // Prepare AJAX data
        const ajaxData = {
            action: 'crear_partido_bet',
            valor_apostado: valorApostado,
            cuota: cuota,
            selected_bet_details: selectedBetDetails,
            ajax_check: 1
        };

        // Send AJAX request
        $.ajax({
            url: 'partidos-bets',
            method: 'POST',
            dataType: 'json',
            data: ajaxData,
            success: function(response) {
                if (response.status === 'success') {
                    swal({
                        title: '¡Apuesta creada exitosamente!',
                        text: 'Valor apostado: ' + valorApostado + '\n' +
                              'Cuota: ' + cuota + '\n' +
                              'Apuestas asociadas: ' + selectedBetDetails.length,
                        icon: 'success',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-success',
                                closeModal: true
                            }
                        }
                    }).then(() => {
                        // Reload the page to show updated data
                        window.location.reload();
                    });
                } else {
                    swal({
                        text: 'Error: ' + response.message,
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-danger',
                                closeModal: true
                            }
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('Response:', xhr.responseText);
                swal({
                    text: 'Error al procesar la solicitud. Por favor, intente nuevamente.',
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn btn-danger',
                            closeModal: true
                        }
                    }
                });
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Deactivation functionality
    window.confirmDeactivateBet = function(betDetailId) {
        // Store the bet detail ID for later use
        $('#deactivateBetModal').data('bet-detail-id', betDetailId);

        // Show the confirmation modal
        $('#deactivateBetModal').modal('show');
    };

    // Handle confirm deactivation
    $('#confirmDeactivateBet').on('click', function() {
        const betDetailId = $('#deactivateBetModal').data('bet-detail-id');

        if (!betDetailId) {
            swal({
                text: 'Error: ID de apuesta no encontrado',
                icon: 'error',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            });
            return;
        }

        // Disable button to prevent double-click
        const confirmBtn = $(this);
        const originalText = confirmBtn.html();
        confirmBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Procesando...');

        // Send AJAX request
        $.ajax({
            url: 'partidos-bets',
            method: 'POST',
            dataType: 'json',
            data: {
                action: 'deactivate_bet_detail',
                bet_detail_id: betDetailId,
                ajax_check: 1
            },
            success: function(response) {
                if (response.status === 'success') {
                    // Hide the modal
                    $('#deactivateBetModal').modal('hide');

                    // Show success message
                    swal({
                        title: '¡Apuesta desactivada exitosamente!',
                        text: response.message,
                        icon: 'success',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-success',
                                closeModal: true
                            }
                        }
                    }).then(() => {
                        // Reload the page to show updated data
                        window.location.reload();
                    });
                } else {
                    // Show error message
                    swal({
                        text: response.message,
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-danger',
                                closeModal: true
                            }
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Error al desactivar la apuesta';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                // Show error message
                swal({
                    text: errorMessage,
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn btn-danger',
                            closeModal: true
                        }
                    }
                });
            },
            complete: function() {
                // Re-enable button
                confirmBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});

// Countdown functionality for match dates
function updateCountdowns() {
    $('.countdown-container').each(function() {
        const container = $(this);
        const matchDateTimeStr = container.data('match-datetime');
        const countdownText = container.find('.countdown-text');
        const badge = container.find('.countdown-badge');

        if (!matchDateTimeStr) return;

        // Create the target date using Bogotá timezone
        const targetDate = new Date(matchDateTimeStr);

        // Get current time in Bogotá timezone
        const now = new Date();
        const bogotaOffset = -5 * 60; // Bogotá is UTC-5
        const nowUtc = now.getTime() + (now.getTimezoneOffset() * 60000);
        const nowBogota = new Date(nowUtc + (bogotaOffset * 60000));

        // Calculate time difference
        const timeDiff = targetDate.getTime() - nowBogota.getTime();

        if (timeDiff > 0) {
            // Future date: Show countdown (existing functionality)
            const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

            // Format countdown text
            let countdownTextStr = '';
            if (days > 0) {
                countdownTextStr += days + ' day' + (days !== 1 ? 's' : '') + ', ';
            }
            if (hours > 0 || days > 0) {
                countdownTextStr += hours + ' hour' + (hours !== 1 ? 's' : '') + ', ';
            }
            countdownTextStr += minutes + ' minute' + (minutes !== 1 ? 's' : '');

            // Set badge style for countdown
            badge.removeClass('bg-warning bg-danger').addClass('bg-info');
            countdownText.html('<i class="fas fa-clock me-1"></i>' + countdownTextStr);
            container.show();
        } else {
            // Past date: Calculate elapsed time
            const elapsedMs = Math.abs(timeDiff);
            const elapsedMinutes = Math.floor(elapsedMs / (1000 * 60));

            if (elapsedMinutes <= 120) {
                // 0-120 minutes ago: Show elapsed time in minutes
                badge.removeClass('bg-info bg-danger').addClass('bg-warning');
                countdownText.html('<i class="fas fa-play me-1"></i>' + elapsedMinutes + ' min');
                container.show();
            } else {
                // >120 minutes ago: Show "TERMINADO" badge
                badge.removeClass('bg-info bg-warning').addClass('bg-danger');
                countdownText.html('<i class="fas fa-flag-checkered me-1"></i>TERMINADO');
                container.show();
            }
        }
    });
}

// Update countdowns immediately and then every minute
$(document).ready(function() {
    updateCountdowns();
    setInterval(updateCountdowns, 60000); // Update every minute
});

// Function to confirm individual bet deactivation
function confirmDeactivateBet(betId) {
    swal({
        title: '¿Confirmar desactivación?',
        text: '¿Está seguro de que desea desactivar esta apuesta? Esta acción no se puede deshacer.',
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Cancelar',
                value: null,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true
            },
            confirm: {
                text: 'Sí, desactivar',
                value: true,
                visible: true,
                className: 'btn btn-danger',
                closeModal: true
            }
        }
    }).then((willDeactivate) => {
        if (willDeactivate) {
            // Send AJAX request to deactivate the bet
            $.ajax({
                url: 'partidos-bets',
                method: 'POST',
                dataType: 'json',
                data: {
                    action: 'bulk_delete_bet_details',
                    selected_bet_details: [betId],
                    ajax_check: 1
                },
                success: function(response) {
                    if (response.status === 'success') {
                        swal({
                            title: '¡Apuesta desactivada!',
                            text: 'La apuesta ha sido desactivada exitosamente.',
                            icon: 'success',
                            buttons: {
                                confirm: {
                                    text: 'Ok',
                                    value: true,
                                    visible: true,
                                    className: 'btn btn-success',
                                    closeModal: true
                                }
                            }
                        }).then(() => {
                            // Reload the page to show updated data
                            window.location.reload();
                        });
                    } else {
                        swal({
                            text: 'Error: ' + response.message,
                            icon: 'error',
                            buttons: {
                                confirm: {
                                    text: 'Ok',
                                    value: true,
                                    visible: true,
                                    className: 'btn btn-danger',
                                    closeModal: true
                                }
                            }
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.error('Response:', xhr.responseText);
                    swal({
                        text: 'Error al procesar la solicitud. Por favor, intente nuevamente.',
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-danger',
                                closeModal: true
                            }
                        }
                    });
                }
            });
        }
    });
}
</script>
<!-- END JavaScript functionality -->

<!-- BEGIN Deactivate Bet Confirmation Modal -->
<div class="modal fade" id="deactivateBetModal" tabindex="-1" aria-labelledby="deactivateBetModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deactivateBetModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirmar Desactivación
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">¿Está seguro que desea desactivar esta apuesta?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    Esta acción desactivará la apuesta y ya no aparecerá en la lista de apuestas disponibles.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeactivateBet">
                    <i class="fas fa-check me-1"></i>Confirmar Desactivación
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END Deactivate Bet Confirmation Modal -->

</body>
</html>
